import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AuthorizedLayoutComponent } from './Components/authorized-layout/authorized-layout.component';
import { UnauthorizedLayoutComponent } from './Components/unauthorized-layout/unauthorized-layout.component';
import { SideBarComponent } from './Components/side-bar/side-bar.component';

@NgModule({
  declarations: [AuthorizedLayoutComponent, UnauthorizedLayoutComponent, SideBarComponent],
  imports: [CommonModule],
  exports: [AuthorizedLayoutComponent, UnauthorizedLayoutComponent],
})
export class SharedModule {}
