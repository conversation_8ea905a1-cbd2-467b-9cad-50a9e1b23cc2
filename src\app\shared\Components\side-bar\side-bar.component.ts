import { Component, OnInit, OnDestroy, inject } from '@angular/core';
import { Router } from '@angular/router';
import { Subject, Observable, combineLatest } from 'rxjs';
import { map, takeUntil } from 'rxjs/operators';
import { DialogService } from 'primeng/dynamicdialog';

import {
  UserProfileService,
  UserProfile,
} from '../../../Core/Services/UserProfile.service';
import {
  ThemeService,
  ThemeMode,
} from '../../../Pages/chattrix/Services/Theme.service';
import { AuthService } from '../../../Pages/authentication/Services/auth.service';
import { LogoutConfirmationDialogComponent } from '../logout-confirmation-dialog/logout-confirmation-dialog.component';
import { DOCUMENT } from '@angular/common';

interface NavigationItem {
  label: string;
  icon: string;
  route?: string;
  action?: () => void;
  requiresAdmin?: boolean;
  badge?: number;
}

@Component({
  selector: 'app-side-bar',
  standalone: false,
  templateUrl: './side-bar.component.html',
  styleUrls: ['./side-bar.component.scss'],
})
export class SideBarComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  #document = inject(DOCUMENT);
  isDarkMode = false;

  // User profile data
  userProfile$: Observable<UserProfile | null>;
  hasAdminAccess$: Observable<boolean>;
  currentTheme$: Observable<ThemeMode>;
  userRoles: string[] = [];

  // Sidebar state
  isCollapsed = false;
  private readonly SIDEBAR_STATE_KEY = 'chat-app-sidebar-collapsed';

  // Navigation items
  navigationItems: NavigationItem[] = [
    {
      label: 'Messages',
      icon: 'pi-comment',
      route: '/dashboard',
      badge: 0,
    },
    {
      label: 'User Management',
      icon: 'pi-users',
      route: '/user-management',
      requiresAdmin: true,
    },
  ];

  constructor(
    private authService: AuthService,
    private dialogService: DialogService,
    public router: Router,
  ) {}

  ngOnInit(): void {
    this.themeService.listenToSystemThemeChanges();
    this.loadSidebarState();
    this.userProfileService
      .getUserRoles()
      .pipe(takeUntil(this.destroy$))
      .subscribe((roles) => {
        this.userRoles = roles;
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  navigateTo(route: string): void {
    this.router.navigate([route]);
  }

  toggleLightDark() {
    const linkElement = this.#document.getElementById(
      'app-theme',
    ) as HTMLLinkElement;
    if (linkElement.href.includes('light')) {
      linkElement.href = 'theme-dark.css';
      this.isDarkMode = true;
    } else {
      linkElement.href = 'theme-light.css';
      this.isDarkMode = false;
    }
  }

  toggleSidebar(): void {
    this.isCollapsed = !this.isCollapsed;
    this.saveSidebarState();
  }

  private loadSidebarState(): void {
    try {
      const saved = localStorage.getItem(this.SIDEBAR_STATE_KEY);
      if (saved !== null) {
        this.isCollapsed = JSON.parse(saved);
      }
    } catch (error) {
      console.warn('Failed to load sidebar state:', error);
    }
  }

  private saveSidebarState(): void {
    try {
      localStorage.setItem(
        this.SIDEBAR_STATE_KEY,
        JSON.stringify(this.isCollapsed),
      );
    } catch (error) {
      console.warn('Failed to save sidebar state:', error);
    }
  }

  onLogout(): void {
    const dialogRef = this.dialogService.open(
      LogoutConfirmationDialogComponent,
      {
        width: '400px',
        style: { 'max-width': '95vw' },
        closable: false,
      },
    );

    dialogRef.onClose.subscribe((confirmed) => {
      if (confirmed) {
        this.themeService.removeTheme();
        this.authService.logout();
      }
    });
  }

  shouldShowNavItem(item: NavigationItem, hasAdminAccess: boolean): boolean {
    if (item.requiresAdmin) {
      const hasOnlyUserRole =
        this.userRoles.length === 1 &&
        this.userRoles[0].toLowerCase() === 'user';

      if (hasOnlyUserRole) {
        return false;
      }

      const hasAdminRole = this.userRoles.some(
        (role) =>
          role.toLowerCase() === 'admin' ||
          role.toLowerCase() === 'super admin' ||
          role.toLowerCase() === 'superadmin',
      );

      return hasAdminRole;
    }
    return true;
  }

  isRouteActive(route?: string): boolean {
    if (!route) return false;

    const currentUrl = this.router.url;

    if (route === '/dashboard') {
      return currentUrl === '/dashboard' || currentUrl === '/';
    }

    const isActive = currentUrl.startsWith(route);

    return isActive;
  }

  onNavItemClick(item: NavigationItem): void {
    if (item.action) {
      item.action();
    } else if (item.route) {
      this.navigateTo(item.route);
    }
  }

  hasAdminAccess() {
    return this.userProfile$.pipe(
      map((profile) => {
        if (!profile) return false;

        const roles = Array.isArray(profile.role)
          ? profile.role
          : [profile.role];

        // Check if user has only "user" role (case-insensitive)
        const hasOnlyUserRole =
          roles.length === 1 && roles[0].toLowerCase() === 'user';

        // If user has only "user" role, they don't have admin access
        if (hasOnlyUserRole) {
          return false;
        }

        // Check for admin or super admin roles (case-insensitive)
        const hasAdminRole = roles.some(
          (role) =>
            role.toLowerCase() === 'admin' ||
            role.toLowerCase() === 'super admin' ||
            role.toLowerCase() === 'superadmin',
        );

        return hasAdminRole;
      }),
    );
  }
}
